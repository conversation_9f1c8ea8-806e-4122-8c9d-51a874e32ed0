import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'providers/auth_provider.dart';
import 'providers/theme_provider.dart';
import 'providers/category_provider.dart';
import 'providers/error_provider.dart';
import 'providers/locale_provider.dart';
import 'providers/manufacturer_provider.dart';
import 'providers/login_log_provider.dart';
import 'providers/user_settings_provider.dart';
import 'providers/permission_provider.dart';
import 'providers/activity_log_provider.dart';
import 'providers/storage_config_provider.dart';
import 'providers/remote_config_provider.dart';
import 'providers/attachment_provider.dart';
import 'services/storage_service.dart';
import 'screens/auth/login_screen.dart';
import 'screens/home/<USER>';
import 'screens/splash_screen.dart';
import 'screens/admin/manufacturers_management_screen.dart';
import 'screens/admin/models_management_screen.dart';
import 'widgets/auth_guard.dart';
import 'screens/admin/login_logs_screen.dart';
import 'screens/admin/data_management_screen.dart';
import 'screens/admin/roles_management_screen.dart';
import 'screens/admin/permissions_reports_screen.dart';
import 'screens/admin/errors_management_screen.dart';
import 'screens/admin/update_management_screen.dart';
import 'screens/admin/cloud_storage_admin_screen.dart';
import 'screens/admin/app_size_management_screen.dart';
import 'screens/errors/errors_list_screen.dart';
import 'screens/favorites/favorites_screen.dart';
import 'models/device_error_model.dart';
import 'utils/theme.dart';
import 'utils/firebase_initializer.dart';
import 'services/firebase_service.dart';
import 'localization/app_localizations.dart';
import 'services/encryption_service.dart';
import 'services/performance_service.dart';
import 'services/app_fingerprint_service.dart';
import 'services/app_signature_service.dart';
import 'services/notification_service.dart';
import 'services/asset_optimization_service.dart';
import 'services/app_directories_service.dart';
import 'services/permissions_service.dart';
import 'services/remote_config_service.dart';
import 'services/attachment_cache_service.dart';
import 'services/auto_optimizer_service.dart';
import 'services/auto_notification_service.dart';
import 'providers/update_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Hide overflow error messages globally
  FlutterError.onError = (FlutterErrorDetails details) {
    // Only show errors in debug mode
    if (kDebugMode) {
      FlutterError.presentError(details);
    }
  };

  // Configure channel buffers to handle lifecycle messages
  ServicesBinding.instance.defaultBinaryMessenger.setMessageHandler(
    'flutter/lifecycle',
    (data) async {
      // Handle lifecycle messages gracefully
      return null;
    },
  );

  // Initialize security services
  EncryptionService.initialize();

  // Verify app integrity and signature
  final isIntegrityValid = await AppFingerprintService.verifyAppIntegrity();
  final isSignatureValid = await AppSignatureService.verifyAppSignature();

  if (!isIntegrityValid || !isSignatureValid) {
    debugPrint('SECURITY WARNING: App integrity or signature verification failed!');
    // In production, you might want to exit the app or show a warning
  }

  // Initialize performance optimization
  await PerformanceService.optimizeBuild();

  try {
    // Initialize Firebase with error handling
    await FirebaseService.initialize();

    // Initialize Remote Config Service early
    try {
      await RemoteConfigService.initialize();
      debugPrint('Remote Config service initialized successfully');
    } catch (e) {
      debugPrint('Warning: Error initializing remote config service: $e');
      // Continue even if remote config initialization fails
    }

    // Initialize notification service
    try {
      await NotificationService.initialize();
      debugPrint('Notification service initialized successfully');
    } catch (e) {
      debugPrint('Warning: Error initializing notification service: $e');
      // Continue even if notification service initialization fails
    }

    // Initialize asset optimization and performance monitoring
    try {
      await AssetOptimizationService.preloadCriticalAssets();
      PerformanceService.startPerformanceMonitoring();
      debugPrint('Asset optimization and performance monitoring initialized');
    } catch (e) {
      debugPrint('Warning: Error initializing optimization services: $e');
    }

    // Initialize size optimization services
    try {
      await AttachmentCacheService.instance.initialize();
      await AutoOptimizerService.instance.initialize();
      debugPrint('Size optimization services initialized');
    } catch (e) {
      debugPrint('Warning: Error initializing size optimization services: $e');
    }

    // Initialize auto notification service
    try {
      await AutoNotificationService.instance.initialize();
      debugPrint('Auto notification service initialized');
    } catch (e) {
      debugPrint('Warning: Error initializing auto notification service: $e');
    }

    // Initialize app directories
    try {
      await AppDirectoriesService.initialize();
      debugPrint('App directories initialized successfully');
    } catch (e) {
      debugPrint('Warning: Error initializing app directories: $e');
    }

    // Request app permissions
    try {
      final permissionsResults = await PermissionsService.requestAllPermissions();
      debugPrint('Permissions requested: $permissionsResults');
    } catch (e) {
      debugPrint('Warning: Error requesting permissions: $e');
    }

    // Initialize Firebase with sample data
    // Wrap in try-catch to prevent app from crashing if this fails
    try {
      await FirebaseInitializer.initializeWithSampleData();
    } catch (e) {
      debugPrint('Warning: Error initializing sample data: $e');
      // Continue even if sample data initialization fails
    }
  } catch (e) {
    debugPrint('Critical error during initialization: $e');
    // Continue anyway to allow the app to at least start
  }

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => CategoryProvider()),
        ChangeNotifierProvider(create: (_) => ErrorProvider()),
        ChangeNotifierProvider(create: (_) => LocaleProvider()),
        ChangeNotifierProvider(create: (_) => ManufacturerProvider()),
        ChangeNotifierProvider(create: (_) => LoginLogProvider()),
        ChangeNotifierProvider(create: (_) => UserSettingsProvider()),
        ChangeNotifierProvider(create: (_) => PermissionProvider()),
        ChangeNotifierProvider(create: (_) => ActivityLogProvider()),
        ChangeNotifierProvider(create: (_) => StorageConfigProvider()),
        ChangeNotifierProvider(create: (_) => RemoteConfigProvider()),
        ChangeNotifierProvider(create: (_) => UpdateProvider()),
        ChangeNotifierProvider(create: (_) => AttachmentProvider()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    // Initialize auth state
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        final authProvider = Provider.of<AuthProvider>(context, listen: false);

        // Set up callback for ErrorProvider initialization
        final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
        authProvider.setOnUserSignedInCallback((userId) async {
          try {
            await errorProvider.initializeWithUser(userId);
          } catch (e) {
            debugPrint('Warning: Error initializing error provider: $e');
          }
        });

        // Initialize auth with error handling
        try {
          await authProvider.initializeAuth();
        } catch (e) {
          debugPrint('Warning: Error initializing auth: $e');
          // Continue even if auth initialization fails
        }

        // Initialize storage service
        try {
          final storageService = StorageService();
          await storageService.initialize();
          debugPrint('Storage service initialized successfully');
        } catch (e) {
          debugPrint('Warning: Error initializing storage service: $e');
          // Continue even if storage service initialization fails
        }

        // Initialize error provider with current user if authenticated
        if (authProvider.isAuthenticated && authProvider.user != null && mounted) {
          try {
            await errorProvider.initializeWithUser(authProvider.user!.id);
          } catch (e) {
            debugPrint('Warning: Error initializing error provider: $e');
            // Continue even if error provider initialization fails
          }
        }

        // Initialize storage config provider
        if (mounted) {
          try {
            final storageConfigProvider = Provider.of<StorageConfigProvider>(context, listen: false);
            await storageConfigProvider.initialize();
            debugPrint('Storage config provider initialized successfully');
          } catch (e) {
            debugPrint('Warning: Error initializing storage config provider: $e');
            // Continue even if storage config provider initialization fails
          }
        }

        // Initialize remote config provider
        if (mounted) {
          try {
            final remoteConfigProvider = Provider.of<RemoteConfigProvider>(context, listen: false);
            await remoteConfigProvider.initialize();
            debugPrint('Remote config provider initialized successfully');
          } catch (e) {
            debugPrint('Warning: Error initializing remote config provider: $e');
            // Continue even if remote config provider initialization fails
          }
        }

        // Initialize update provider
        if (mounted) {
          try {
            final updateProvider = Provider.of<UpdateProvider>(context, listen: false);
            await updateProvider.initialize();
            debugPrint('Update provider initialized successfully');
          } catch (e) {
            debugPrint('Warning: Error initializing update provider: $e');
            // Continue even if update provider initialization fails
          }
        }

        // Initialize attachment provider
        if (mounted) {
          try {
            final attachmentProvider = Provider.of<AttachmentProvider>(context, listen: false);
            await attachmentProvider.initialize();
            debugPrint('Attachment provider initialized successfully');
          } catch (e) {
            debugPrint('Warning: Error initializing attachment provider: $e');
            // Continue even if attachment provider initialization fails
          }
        }
      } catch (e) {
        debugPrint('Critical error during app initialization: $e');
      } finally {
        if (mounted) {
          setState(() {
            _isInitialized = true;
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);

    return MaterialApp(
      onGenerateTitle: (context) => context.t('app_title'),
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeProvider.isDarkMode ? ThemeMode.dark : ThemeMode.light,
      debugShowCheckedModeBanner: false,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: AppLocalizations.supportedLocales,
      locale: localeProvider.locale,
      // Set text direction based on locale and hide overflow errors
      builder: (context, child) {
        // Hide overflow error messages in production
        ErrorWidget.builder = (FlutterErrorDetails errorDetails) {
          return Container(
            color: Colors.transparent,
            child: const SizedBox.shrink(),
          );
        };

        return Directionality(
          textDirection: localeProvider.isRTL ? TextDirection.rtl : TextDirection.ltr,
          child: child!,
        );
      },
      home: !_isInitialized
          ? const SplashScreen()
          : authProvider.isAuthenticated
              ? const HomeScreen()
              : const LoginScreen(),
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/login':
            return MaterialPageRoute(builder: (context) => const LoginScreen());
          case '/home':
            return MaterialPageRoute(
              builder: (context) => const AuthGuard(child: HomeScreen()),
            );
          case '/add-error':
            final args = settings.arguments as DeviceError?;
            return MaterialPageRoute(
              builder: (context) => AuthGuard(
                requireAdmin: true,
                child: ErrorsManagementScreen(errorToEdit: args),
              ),
            );
          case '/errors':
            return MaterialPageRoute(
              builder: (context) => const AuthGuard(child: ErrorsListScreen()),
            );
          case '/favorites':
            return MaterialPageRoute(
              builder: (context) => const AuthGuard(child: FavoritesScreen()),
            );
          case '/manufacturers-management':
            return MaterialPageRoute(
              builder: (context) => const AuthGuard(
                requireAdmin: true,
                child: ManufacturersManagementScreen(),
              ),
            );
          case '/models-management':
            final args = settings.arguments as String?;
            if (args == null) {
              return MaterialPageRoute(builder: (context) => const HomeScreen());
            }
            return MaterialPageRoute(
              builder: (context) => AuthGuard(
                requireAdmin: true,
                child: ModelsManagementScreen(manufacturer: args),
              ),
            );
          case '/login-logs':
            final args = settings.arguments as String?;
            return MaterialPageRoute(
              builder: (context) => AuthGuard(
                requireAdmin: true,
                child: LoginLogsScreen(userId: args),
              ),
            );
          case '/data-management':
            return MaterialPageRoute(
              builder: (context) => const AuthGuard(
                requireAdmin: true,
                child: DataManagementScreen(),
              ),
            );
          case '/roles-management':
            return MaterialPageRoute(
              builder: (context) => const AuthGuard(
                requireAdmin: true,
                child: RolesManagementScreen(),
              ),
            );
          case '/permissions-reports':
            return MaterialPageRoute(
              builder: (context) => const AuthGuard(
                requireAdmin: true,
                child: PermissionsReportsScreen(),
              ),
            );
          case '/update-management':
            return MaterialPageRoute(
              builder: (context) => const AuthGuard(
                requireAdmin: true,
                child: UpdateManagementScreen(),
              ),
            );
          case '/storage-config':
            return MaterialPageRoute(
              builder: (context) => const AuthGuard(
                requireAdmin: true,
                child: CloudStorageAdminScreen(),
              ),
            );
          case '/app-size-management':
            return MaterialPageRoute(
              builder: (context) => const AuthGuard(
                requireAdmin: true,
                child: AppSizeManagementScreen(),
              ),
            );
          default:
            return MaterialPageRoute(builder: (context) => const HomeScreen());
        }
      },
    );
  }
}


