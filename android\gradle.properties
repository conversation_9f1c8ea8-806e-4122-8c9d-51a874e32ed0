# Project-wide Gradle settings

# Cache settings
org.gradle.caching=true
org.gradle.daemon=true
org.gradle.parallel=true

# Kotlin daemon settings
kotlin.daemon.jvmargs=-Xmx2G -XX:MaxMetaspaceSize=1G -XX:ReservedCodeCacheSize=256m -XX:+UseG1GC
kotlin.incremental=true
kotlin.incremental.android=true
kotlin.caching.enabled=true
kotlin.daemon.useFallbackStrategy=true
kotlin.compiler.execution.strategy=in-process

# Java compilation settings
org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
android.defaults.buildfeatures.buildconfig=true
android.nonTransitiveRClass=false
android.enableJetifier=true
android.useAndroidX=true

# Network and TLS settings
systemProp.https.protocols=TLSv1.2,TLSv1.3
systemProp.http.proxyHost=
systemProp.http.proxyPort=
systemProp.https.proxyHost=
systemProp.https.proxyPort=
systemProp.javax.net.ssl.trustStore=
systemProp.javax.net.ssl.trustStorePassword=
systemProp.javax.net.ssl.keyStore=
systemProp.javax.net.ssl.keyStorePassword=

# Android settings
android.defaults.buildfeatures.buildconfig=true
android.nonTransitiveRClass=false
