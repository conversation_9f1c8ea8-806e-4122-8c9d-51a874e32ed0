import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/locale_provider.dart';
import '../../providers/manufacturer_provider.dart';
import '../../widgets/gradient_background.dart';
import '../../widgets/modern_gradient_background.dart';
import '../../utils/theme.dart';
import 'cloud_storage_admin_screen.dart';
import '../../widgets/responsive_layout.dart';
import 'users_management_screen.dart';
import 'categories_management_screen.dart';
import 'errors_management_screen.dart';
import 'manufacturers_management_screen.dart';
import 'models_management_screen.dart';
import 'login_history_screen.dart';
import 'data_management_screen.dart';
import 'app_size_management_screen.dart';
import 'notification_management_screen.dart';
import 'file_manager_screen.dart';
import 'security_info_screen.dart';
import 'firebase_fingerprint_screen.dart';
import 'remote_config_admin_screen.dart';
import 'app_settings_screen.dart';
import 'google_drive_setup_screen.dart';

class AdminPanelScreen extends StatefulWidget {
  const AdminPanelScreen({super.key});

  @override
  State<AdminPanelScreen> createState() => _AdminPanelScreenState();
}

class _AdminPanelScreenState extends State<AdminPanelScreen> {
  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);

    // Check if user is admin
    if (!authProvider.isAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: const Text(
            'Unauthorized / غير مصرح',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
          elevation: 4,
          iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
          centerTitle: true,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error,
                size: 80,
                color: Colors.red,
              ),
              SizedBox(height: 16),
              Text(
                'You are not authorized to access the admin panel\nغير مصرح لك بالوصول إلى لوحة الإدارة',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 18),
              ),
            ],
          ),
        ),
      );
    }

    return GradientScaffold(
      appBar: AppBar(
        title: Text(
          localeProvider.isRTL ? 'لوحة الإدارة' : 'Admin Panel',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF2563EB), // خلفية زرقاء
        elevation: 4,
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: true,
      ),
      body: ResponsivePadding(
        mobilePadding: const EdgeInsets.all(12.0),
        tabletPadding: const EdgeInsets.all(16.0),
        desktopPadding: const EdgeInsets.all(20.0),
        child: ResponsiveGridView(
          mobileColumns: 2,
          tabletColumns: 3,
          desktopColumns: 4,
          spacing: context.responsive(12.0, tablet: 16.0, desktop: 20.0),
          runSpacing: context.responsive(12.0, tablet: 16.0, desktop: 20.0),
          childAspectRatio: context.responsive(1.0, tablet: 1.1, desktop: 1.2),
          children: [
            _buildAdminCard(
              context,
              icon: Icons.people,
              title: localeProvider.isRTL ? 'إدارة المستخدمين' : 'User Management',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const UsersManagementScreen(),
                  ),
                );
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.category,
              title: localeProvider.isRTL ? 'إدارة الفئات' : 'Category Management',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CategoriesManagementScreen(),
                  ),
                );
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.error,
              title: localeProvider.isRTL ? 'إدارة الأعطال' : 'Error Management',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ErrorsManagementScreen(),
                  ),
                );
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.business,
              title: localeProvider.isRTL ? 'إدارة الشركات المصنعة' : 'Manufacturers Management',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ManufacturersManagementScreen(),
                  ),
                );
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.devices,
              title: localeProvider.isRTL ? 'إدارة الموديلات' : 'Models Management',
              onTap: () {
                // Show a dialog to select a manufacturer first
                _showSelectManufacturerDialog(context);
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.history,
              title: localeProvider.isRTL ? 'سجل تسجيل الدخول' : 'Login History',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const LoginHistoryScreen(),
                  ),
                );
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.cloud,
              title: localeProvider.isRTL ? 'إدارة التخزين السحابي' : 'Cloud Storage Management',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CloudStorageAdminScreen(),
                  ),
                );
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.import_export,
              title: localeProvider.isRTL ? 'إدارة البيانات' : 'Data Management',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const DataManagementScreen(),
                  ),
                );
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.admin_panel_settings,
              title: localeProvider.isRTL ? 'إدارة الأدوار والصلاحيات' : 'Roles & Permissions',
              onTap: () {
                Navigator.pushNamed(context, '/roles-management');
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.analytics,
              title: localeProvider.isRTL ? 'تقارير الصلاحيات' : 'Permissions Reports',
              onTap: () {
                Navigator.pushNamed(context, '/permissions-reports');
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.folder_open,
              title: localeProvider.isRTL ? 'إدارة الملفات' : 'File Manager',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const FileManagerScreen(),
                  ),
                );
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.security,
              title: localeProvider.isRTL ? 'معلومات الأمان' : 'Security Info',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SecurityInfoScreen(),
                  ),
                );
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.fingerprint,
              title: localeProvider.isRTL ? 'Firebase Fingerprint' : 'Firebase Fingerprint',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const FirebaseFingerprintScreen(),
                  ),
                );
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.system_update_alt,
              title: localeProvider.isRTL ? 'إدارة التحديثات' : 'Update Management',
              onTap: () {
                Navigator.pushNamed(context, '/update-management');
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.cloud_sync,
              title: localeProvider.isRTL ? 'إدارة التكوين البعيد' : 'Remote Config Management',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const RemoteConfigAdminScreen(),
                  ),
                );
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.settings,
              title: localeProvider.isRTL ? 'إعدادات التطبيق' : 'App Settings',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AppSettingsScreen(),
                  ),
                );
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.storage,
              title: localeProvider.isRTL ? 'إدارة حجم التطبيق' : 'App Size Management',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AppSizeManagementScreen(),
                  ),
                );
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.notifications_active,
              title: localeProvider.isRTL ? 'إدارة الإشعارات التلقائية' : 'Auto Notifications Management',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const NotificationManagementScreen(),
                  ),
                );
              },
            ),
            _buildAdminCard(
              context,
              icon: Icons.cloud_upload,
              title: localeProvider.isRTL ? 'إعداد Google Drive' : 'Google Drive Setup',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const GoogleDriveSetupScreen(),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdminCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = AppTheme.primaryBlue;

    return ModernCard(
      margin: const EdgeInsets.all(4),
      padding: const EdgeInsets.all(20),
      backgroundColor: isDarkMode ? AppTheme.cardDark : const Color(0xFFF8FAFF), // خلفية زرقاء فاتحة جداً
      borderRadius: BorderRadius.circular(20),
      boxShadow: [
        BoxShadow(
          color: isDarkMode
              ? Colors.black.withValues(alpha: 0.3)
              : const Color(0xFF2563EB).withValues(alpha: 0.12), // ظل أزرق فاتح
          blurRadius: 20,
          offset: const Offset(0, 8),
          spreadRadius: 2,
        ),
      ],
      border: Border.all(
        color: isDarkMode
            ? primaryColor.withValues(alpha: 0.3)
            : const Color(0xFF2563EB).withValues(alpha: 0.2), // حدود زرقاء فاتحة
        width: 1,
      ),
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ModernIconContainer(
            icon: icon,
            iconColor: primaryColor,
            backgroundColor: primaryColor,
            size: 64,
            iconSize: 32,
          ),
          const SizedBox(height: 20),
          Flexible(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: isDarkMode
                    ? Colors.white
                    : AppTheme.textPrimary,
                letterSpacing: 0.3,
                height: 1.3,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  void _showSelectManufacturerDialog(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final manufacturerProvider = Provider.of<ManufacturerProvider>(context, listen: false);

    // Fetch manufacturers if not already loaded
    if (manufacturerProvider.manufacturers.isEmpty) {
      manufacturerProvider.fetchManufacturers();
    }

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                localeProvider.isRTL ? 'اختر الشركة المصنعة' : 'Select Manufacturer',
              ),
              content: SizedBox(
                width: double.maxFinite,
                child: manufacturerProvider.isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : manufacturerProvider.manufacturers.isEmpty
                        ? Center(
                            child: Text(
                              localeProvider.isRTL
                                  ? 'لا توجد شركات مصنعة'
                                  : 'No manufacturers found',
                              style: const TextStyle(fontSize: 16),
                            ),
                          )
                        : ListView.builder(
                            shrinkWrap: true,
                            itemCount: manufacturerProvider.manufacturers.length,
                            itemBuilder: (context, index) {
                              final manufacturer = manufacturerProvider.manufacturers[index];
                              return ListTile(
                                title: Text(manufacturer),
                                onTap: () {
                                  Navigator.pop(context);
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => ModelsManagementScreen(
                                        manufacturer: manufacturer,
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(localeProvider.isRTL ? 'إلغاء' : 'Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ManufacturersManagementScreen(),
                      ),
                    );
                  },
                  child: Text(localeProvider.isRTL ? 'إدارة الشركات المصنعة' : 'Manage Manufacturers'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
